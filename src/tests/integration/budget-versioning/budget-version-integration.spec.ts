import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { createClient } from '@supabase/supabase-js';
import type { Database } from '$lib/database.types';
import { PUBLIC_SUPABASE_URL, PUBLIC_SUPABASE_ANON_KEY } from '$env/static/public';
import { SUPABASE_SERVICE_KEY } from '$env/static/private';

// Integration tests for budget versioning system
// These tests use a real Supabase connection to test the full system
// Only run if required Supabase env vars are present; otherwise skip to keep unit suite green
const hasSupabaseEnv = Boolean(
	PUBLIC_SUPABASE_URL && SUPABASE_SERVICE_KEY && PUBLIC_SUPABASE_ANON_KEY,
);

const maybeDescribe = hasSupabaseEnv ? describe : describe.skip;

maybeDescribe('Budget Versioning Integration Tests', () => {
	let supabase: ReturnType<typeof createClient<Database>>;
	let adminSupabase: ReturnType<typeof createClient<Database>>;
	let testProjectId: string;
	let testUserId: string;
	let testOrgId: string;
	let testClientId: string;
	let testWbsLibraryId: string;

	beforeEach(async () => {
		// Create admin Supabase client for user management and cleanup
		adminSupabase = createClient<Database>(PUBLIC_SUPABASE_URL!, SUPABASE_SERVICE_KEY!);

		// Create regular Supabase client for user operations (respects RLS)
		supabase = createClient<Database>(PUBLIC_SUPABASE_URL!, PUBLIC_SUPABASE_ANON_KEY!);

		// Create test data
		await setupTestData();
	});

	afterEach(async () => {
		// Clean up test data
		await cleanupTestData();
	});

	async function setupTestData() {
		// Create test user using admin client with unique email
		const uniqueEmail = `test-budget-versioning-${Date.now()}-${Math.random().toString(36).substring(2, 11)}@example.com`;
		const { data: userData, error: userError } = await adminSupabase.auth.admin.createUser({
			email: uniqueEmail,
			password: 'testpassword123',
			email_confirm: true,
		});

		if (userError) throw userError;
		testUserId = userData.user.id;

		// Sign in as the test user for RPC calls using regular client
		const { error: signInError } = await supabase.auth.signInWithPassword({
			email: uniqueEmail,
			password: 'testpassword123',
		});

		if (signInError) throw signInError;

		// Ensure profile exists for the user (required for foreign key constraint)
		// Use admin client to bypass RLS for profile creation
		const { error: profileError } = await adminSupabase.from('profile').upsert({
			user_id: testUserId,
			email: uniqueEmail,
			full_name: 'Test Integration User',
		});

		if (profileError) throw profileError;

		// Create test organization with unique name
		// Use admin client to bypass RLS for organization creation
		const uniqueOrgName = `Test Budget Versioning Org ${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
		const { data: orgData, error: orgError } = await adminSupabase
			.from('organization')
			.insert({
				name: uniqueOrgName,
				created_by_user_id: testUserId,
			})
			.select()
			.single();

		if (orgError) throw orgError;
		testOrgId = orgData.org_id;

		// Create organization membership
		await supabase.from('membership').insert({
			user_id: testUserId,
			entity_type: 'organization',
			entity_id: testOrgId,
			role: 'admin',
		});

		// Create test client
		// Use admin client to bypass RLS for client creation
		const { data: clientData, error: clientError } = await adminSupabase
			.from('client')
			.insert({
				name: 'Test Budget Versioning Client',
				org_id: testOrgId,
				created_by_user_id: testUserId,
			})
			.select()
			.single();

		if (clientError) throw clientError;
		testClientId = clientData.client_id;

		// Create test WBS library with unique name
		// Use admin client to bypass RLS for WBS library creation
		const uniqueWbsLibName = `Test Budget Versioning WBS ${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
		const { data: wbsLibData, error: wbsLibError } = await adminSupabase
			.from('wbs_library')
			.insert({
				name: uniqueWbsLibName,
			})
			.select()
			.single();

		if (wbsLibError) throw wbsLibError;
		testWbsLibraryId = wbsLibData.wbs_library_id;

		// Create test project
		// Use admin client to bypass RLS for project creation
		const { data: projectData, error: projectError } = await adminSupabase
			.from('project')
			.insert({
				name: 'Test Budget Versioning Project',
				client_id: testClientId,
				wbs_library_id: testWbsLibraryId,
				created_by_user_id: testUserId,
			})
			.select()
			.single();

		if (projectError) throw projectError;
		testProjectId = projectData.project_id;

		// Create some test WBS items
		await supabase.from('wbs_library_item').insert([
			{
				wbs_library_id: testWbsLibraryId,
				code: '1',
				description: 'Test Category',
				item_type: 'Custom' as const,
				in_level_code: '1',
				level: 1,
				client_id: testClientId,
			},
			{
				wbs_library_id: testWbsLibraryId,
				code: '1.1',
				description: 'Test Detail Item',
				item_type: 'Custom' as const,
				parent_code: '1',
				in_level_code: '1',
				level: 2,
				client_id: testClientId,
			},
		]);
	}

	async function cleanupTestData() {
		// Sign out to avoid interference between tests
		await supabase.auth.signOut();

		if (testProjectId) {
			// Delete project (cascades to related data) using admin client
			await adminSupabase.from('project').delete().eq('project_id', testProjectId);
		}
		if (testClientId) {
			await adminSupabase.from('client').delete().eq('client_id', testClientId);
		}
		if (testOrgId) {
			await adminSupabase.from('organization').delete().eq('org_id', testOrgId);
		}
		if (testUserId) {
			await adminSupabase.auth.admin.deleteUser(testUserId);
		}
	}

	describe('Budget Version Lifecycle', () => {
		it('should create initial budget version for new project', async () => {
			// Check that project has an active budget version
			const { data: project, error: projectError } = await supabase
				.from('project')
				.select('active_budget_version_id')
				.eq('project_id', testProjectId)
				.single();

			expect(projectError).toBeNull();
			expect(project?.active_budget_version_id).toBeTruthy();

			// Verify the budget version exists
			const { data: version, error: versionError } = await supabase
				.from('budget_version')
				.select('*')
				.eq('budget_version_id', project!.active_budget_version_id!)
				.single();

			expect(versionError).toBeNull();
			expect(version?.project_id).toBe(testProjectId);
			expect(version?.kind).toBe('system');
		});

		it('should create and activate new budget version', async () => {
			// Create a new budget version
			const { data: newVersionId, error: createError } = await supabase.rpc(
				'create_budget_version',
				{
					p_project_id: testProjectId,
					p_label: 'Test Manual Version',
					p_kind: 'manual',
				},
			);

			expect(createError).toBeNull();
			expect(newVersionId).toBeTruthy();

			// Activate the new version
			const { error: activateError } = await supabase.rpc('activate_budget_version', {
				p_version_id: newVersionId!,
				p_reason: 'Integration test activation',
			});

			expect(activateError).toBeNull();

			// Verify project now points to new version
			const { data: project, error: projectError } = await supabase
				.from('project')
				.select('active_budget_version_id')
				.eq('project_id', testProjectId)
				.single();

			expect(projectError).toBeNull();
			expect(project?.active_budget_version_id).toBe(newVersionId);
		});

		it('should maintain version lineage through prev_version_id', async () => {
			// Get current active version
			const { data: project } = await supabase
				.from('project')
				.select('active_budget_version_id')
				.eq('project_id', testProjectId)
				.single();

			const originalVersionId = project!.active_budget_version_id;

			// Create new version (should link to current active)
			const { data: newVersionId } = await supabase.rpc('create_budget_version', {
				p_project_id: testProjectId,
				p_label: 'Lineage Test Version',
				p_kind: 'manual',
			});

			// Verify lineage
			const { data: newVersion } = await supabase
				.from('budget_version')
				.select('prev_version_id')
				.eq('budget_version_id', newVersionId!)
				.single();

			expect(newVersion?.prev_version_id).toBe(originalVersionId);
		});
	});

	describe('Budget Import Integration', () => {
		it('should apply budget import and create new version', async () => {
			const importItems = [
				{
					code: '1.1',
					description: 'Test Detail Item',
					quantity: 10,
					unit: 'each',
					material_rate: 100,
					factor: 1,
				},
			];

			const { data: importResult, error: importError } = await supabase.rpc('apply_budget_import', {
				p_project_id: testProjectId,
				p_source_filename: 'test-import.xlsx',
				p_items: importItems,
				p_source_hash: 'test-hash-123',
				p_notes: 'Integration test import',
			});

			expect(importError).toBeNull();
			expect(importResult).toBeTruthy();

			const result = importResult as any;
			expect(result.new_version_id).toBeTruthy();
			expect(result.budget_import_id).toBeTruthy();

			// Verify budget version was created
			const { data: version } = await supabase
				.from('budget_version')
				.select('*')
				.eq('budget_version_id', result.new_version_id)
				.single();

			expect(version?.kind).toBe('import');
			expect(version?.project_id).toBe(testProjectId);

			// Verify budget items were created
			const { data: items } = await supabase
				.from('budget_version_item')
				.select('*')
				.eq('budget_version_id', result.new_version_id);

			expect(items).toHaveLength(1);
			expect(items![0].quantity).toBe(10);
			expect(items![0].material_rate).toBe(100);
		});

		it('should detect duplicate imports', async () => {
			const importItems = [
				{
					code: '1.1',
					description: 'Duplicate Test Item',
					quantity: 5,
					unit: 'each',
					material_rate: 50,
					factor: 1,
				},
			];

			const sourceHash = 'duplicate-test-hash';

			// First import
			const { data: firstImport } = await supabase.rpc('apply_budget_import', {
				p_project_id: testProjectId,
				p_source_filename: 'duplicate-test.xlsx',
				p_items: importItems,
				p_source_hash: sourceHash,
			});

			// Check for existing import
			const { data: existingImport } = await supabase.rpc('find_existing_import', {
				p_project_id: testProjectId,
				p_source_hash: sourceHash,
			});

			expect(existingImport).toHaveLength(1);
			expect(existingImport![0].exists).toBe(true);
			expect(existingImport![0].budget_import_id).toBe((firstImport as any).budget_import_id);
		});
	});

	describe('Stage Integration', () => {
		it('should create budget version on stage completion', async () => {
			// Create a project stage
			const { data: stage, error: stageError } = await supabase
				.from('project_stage')
				.insert({
					project_id: testProjectId,
					name: 'Test Stage',
					stage_order: 1,
				})
				.select()
				.single();

			expect(stageError).toBeNull();

			// Complete the stage (this should create a budget version)
			const { data: snapshotId, error: completeError } = await supabase.rpc(
				'complete_project_stage',
				{
					p_project_stage_id: stage!.project_stage_id,
					p_completion_notes: 'Integration test completion',
				},
			);

			expect(completeError).toBeNull();
			expect(snapshotId).toBeTruthy();

			// Verify budget snapshot was created with version link
			const { data: snapshot } = await supabase
				.from('budget_snapshot')
				.select('budget_version_id')
				.eq('budget_snapshot_id', snapshotId!)
				.single();

			expect(snapshot?.budget_version_id).toBeTruthy();

			// Verify budget version was created with stage kind
			const { data: version } = await supabase
				.from('budget_version')
				.select('*')
				.eq('budget_version_id', snapshot!.budget_version_id!)
				.single();

			expect(version?.kind).toBe('stage');
			expect(version?.stage_id).toBe(stage!.project_stage_id);
		});
	});

	describe('Version Comparison', () => {
		it('should generate diff between versions', async () => {
			// Create two versions with different data
			const { data: version1Id } = await supabase.rpc('create_budget_version', {
				p_project_id: testProjectId,
				p_label: 'Version 1',
			});

			const { data: version2Id } = await supabase.rpc('create_budget_version', {
				p_project_id: testProjectId,
				p_label: 'Version 2',
			});

			// Get WBS item for testing
			const { data: wbsItems, error: wbsItemError } = await supabase
				.from('wbs_library_item')
				.select('wbs_library_item_id')
				.eq('code', '1.1')
				.eq('wbs_library_id', testWbsLibraryId);

			if (wbsItemError || !wbsItems || wbsItems.length === 0) {
				throw new Error(`WBS item not found: ${wbsItemError?.message || 'No items returned'}`);
			}

			const wbsItem = wbsItems[0]; // Use the first item if multiple found

			// Add different items to each version
			await supabase.from('budget_version_item').insert([
				{
					budget_version_id: version1Id!,
					wbs_library_item_id: wbsItem!.wbs_library_item_id,
					quantity: 10,
					unit_rate: 100,
					material_rate: 0,
				},
				{
					budget_version_id: version2Id!,
					wbs_library_item_id: wbsItem!.wbs_library_item_id,
					quantity: 15,
					unit_rate: 120,
					material_rate: 0,
				},
			]);

			// Generate diff
			const { data: diff, error: diffError } = await supabase.rpc('diff_budget_versions', {
				p_version_a: version1Id!,
				p_version_b: version2Id!,
			});

			expect(diffError).toBeNull();
			expect(diff).toBeTruthy();
			const diffResult = diff as any;
			expect(diffResult.summary).toBeTruthy();
			expect(diffResult.summary.total_cost_delta).toBeGreaterThan(0);
		});
	});
});
